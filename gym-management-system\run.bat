@echo off
echo Compiling the project...

:: Create the output directory if it doesn't exist
if not exist "bin" mkdir bin

:: Compile the project
javac -d bin -cp "src;lib/*" src/Main.java src/controllers/*.java src/models/*.java src/utils/*.java

:: Check if compilation was successful
if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b %errorlevel%
)

echo Compilation successful!
echo Running the application...

:: Run the application
java -cp "bin;lib/*" Main

pause
