<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.VBox?>

<VBox xmlns:fx="http://javafx.com/fxml" alignment="CENTER" spacing="20">
    <Label text="Registration Form" style="-fx-font-size: 24px; -fx-font-weight: bold;"/>
    <GridPane hgap="10" vgap="10">
        <Label text="Nom:"/>
        <TextField fx:id="nomField" />

        <Label text="Prénom:"/>
        <TextField fx:id="prenomField" />

        <Label text="Email:"/>
        <TextField fx:id="emailField" />

        <Label text="Téléphone:"/>
        <TextField fx:id="telephoneField" />

        <Label text="Date de Naissance:"/>
        <DatePicker fx:id="dateNaissancePicker" />

        <Label text="Type d'Abonnement:"/>
        <TextField fx:id="abonnementField" />

        <Button text="S'inscrire" fx:id="registerButton" />
    </GridPane>
</VBox>