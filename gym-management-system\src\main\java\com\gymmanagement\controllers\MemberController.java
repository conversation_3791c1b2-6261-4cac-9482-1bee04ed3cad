package com.gymmanagement.controllers;

import javafx.fxml.FXML;
import javafx.scene.control.TextField;
import javafx.scene.control.TableView;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.gymmanagement.models.Member;
import com.gymmanagement.models.Registration;
import com.gymmanagement.utils.DatabaseConnection;

public class MemberController {

    @FXML
    private TextField nomField;

    @FXML
    private TextField prenomField;

    @FXML
    private TextField emailField;

    @FXML
    private TextField telephoneField;

    @FXML
    private TableView<?> membersTable;

    @FXML
    private void handleAddMember() {
        // Logic to handle adding a member
        System.out.println("Member added: " + nomField.getText() + " " + prenomField.getText());
    }

    // Method to create a new member
    public void createMember(Member member) {
        String query = "INSERT INTO membres (nom, prenom, email, telephone, date_naissance) VALUES (?, ?, ?, ?, ?)";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setString(1, member.getNom());
            statement.setString(2, member.getPrenom());
            statement.setString(3, member.getEmail());
            statement.setString(4, member.getTelephone());
            statement.setDate(5, java.sql.Date.valueOf(member.getDateNaissance()));
            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // Method to read member details
    public Member readMember(int memberId) {
        String query = "SELECT * FROM membres WHERE id = ?";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setInt(1, memberId);
            ResultSet resultSet = statement.executeQuery();
            if (resultSet.next()) {
                return new Member(
                    resultSet.getInt("id"),
                    resultSet.getString("nom"),
                    resultSet.getString("prenom"),
                    resultSet.getString("email"),
                    resultSet.getString("telephone"),
                    resultSet.getDate("date_naissance").toLocalDate(),
                    resultSet.getDate("date_inscription").toLocalDate()
                );
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }

    // Method to update member details
    public void updateMember(Member member) {
        String query = "UPDATE membres SET nom = ?, prenom = ?, email = ?, telephone = ?, date_naissance = ? WHERE id = ?";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setString(1, member.getNom());
            statement.setString(2, member.getPrenom());
            statement.setString(3, member.getEmail());
            statement.setString(4, member.getTelephone());
            statement.setDate(5, java.sql.Date.valueOf(member.getDateNaissance()));
            statement.setInt(6, member.getId());
            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // Method to delete a member
    public void deleteMember(int memberId) {
        String query = "DELETE FROM membres WHERE id = ?";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setInt(1, memberId);
            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // Method to list all members
    public List<Member> listAllMembers() {
        List<Member> members = new ArrayList<>();
        String query = "SELECT * FROM membres";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query);
             ResultSet resultSet = statement.executeQuery()) {
            while (resultSet.next()) {
                members.add(new Member(
                    resultSet.getInt("id"),
                    resultSet.getString("nom"),
                    resultSet.getString("prenom"),
                    resultSet.getString("email"),
                    resultSet.getString("telephone"),
                    resultSet.getDate("date_naissance").toLocalDate(),
                    resultSet.getDate("date_inscription").toLocalDate()
                ));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return members;
    }

    // Populate registrations for a member
    public List<Registration> getRegistrationsForMember(int memberId) {
        List<Registration> registrations = new ArrayList<>();
        String query = "SELECT * FROM inscriptions WHERE membre_id = ?";
        try (Connection connection = DatabaseConnection.getConnection();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setInt(1, memberId);
            ResultSet resultSet = statement.executeQuery();
            while (resultSet.next()) {
                registrations.add(new Registration(
                    resultSet.getInt("id"),
                    null, // Member can be set later if needed
                    new com.gymmanagement.controllers.SubscriptionController().readSubscription(resultSet.getInt("abonnement_id")),
                    resultSet.getDate("date_debut").toLocalDate(),
                    resultSet.getDate("date_fin").toLocalDate(),
                    resultSet.getString("etat")
                ));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return registrations;
    }
}