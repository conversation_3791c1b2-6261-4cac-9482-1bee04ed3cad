<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.GridPane?>

<AnchorPane xmlns:fx="http://javafx.com/fxml" prefHeight="400.0" prefWidth="600.0">
    <GridPane alignment="CENTER" hgap="10" vgap="10">
        <Label text="Type d'abonnement:" />
        <TextField fx:id="typeField" />
        
        <Label text="Durée (jours):" />
        <TextField fx:id="dureeField" />
        
        <Label text="Prix:" />
        <TextField fx:id="prixField" />
        
        <Button text="Ajouter" onAction="#handleAddSubscription" />
        <Button text="Modifier" onAction="#handleUpdateSubscription" />
        <Button text="Supprimer" onAction="#handleDeleteSubscription" />
        
        <TableView fx:id="subscriptionTable">
            <!-- Define columns for the table here -->
        </TableView>
    </GridPane>
</AnchorPane>