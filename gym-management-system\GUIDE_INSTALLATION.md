# Guide d'Installation - Système de Gestion de Salle de Sport

## 📋 Prérequis système

### Configuration minimale requise
- **Système d'exploitation** : Windows 10/11, macOS 10.14+, ou Linux Ubuntu 18.04+
- **Mémoire RAM** : 4 GB minimum, 8 GB recommandé
- **Espace disque** : 2 GB d'espace libre
- **Résolution écran** : 1024x768 minimum, 1920x1080 recommandé

### Logiciels requis
- **Java JDK** : Version 11 ou supérieure
- **XAMPP** : Pour MySQL (ou MySQL Server standalone)
- **Maven** : Version 3.6+ (optionnel, inclus dans la plupart des IDE)

## 🔧 Installation étape par étape

### Étape 1 : Installation de Java JDK

#### Windows
1. Télécharger Java JDK 11+ depuis [Oracle](https://www.oracle.com/java/technologies/downloads/) ou [OpenJDK](https://openjdk.org/)
2. Exécuter l'installateur et suivre les instructions
3. Configurer la variable d'environnement `JAVA_HOME`
4. Vérifier l'installation : `java -version` dans l'invite de commande

#### macOS
```bash
# Avec Homebrew
brew install openjdk@11

# Ou télécharger depuis le site officiel
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install openjdk-11-jdk
```

### Étape 2 : Installation de XAMPP

#### Windows
1. Télécharger XAMPP depuis [apachefriends.org](https://www.apachefriends.org/)
2. Exécuter l'installateur en tant qu'administrateur
3. Sélectionner Apache et MySQL lors de l'installation
4. Démarrer XAMPP Control Panel
5. Démarrer les services Apache et MySQL

#### macOS/Linux
```bash
# Télécharger et installer XAMPP
# Ou utiliser MySQL standalone
sudo apt install mysql-server  # Linux
brew install mysql             # macOS
```

### Étape 3 : Configuration de la base de données

#### 3.1 Accès à MySQL
1. Ouvrir XAMPP Control Panel
2. Cliquer sur "Admin" pour MySQL (ouvre phpMyAdmin)
3. Ou utiliser la ligne de commande :
```bash
mysql -u root -p
```

#### 3.2 Création de la base de données
```sql
CREATE DATABASE gymdb;
USE gymdb;
```

#### 3.3 Exécution du script de création
1. **Méthode 1 - Script automatique** :
   ```bash
   cd gym-management-system
   ./setup-database.bat  # Windows
   ./setup-database.sh   # Linux/macOS
   ```

2. **Méthode 2 - Manuel** :
   - Copier le contenu de `setup-database.sql`
   - L'exécuter dans phpMyAdmin ou ligne de commande MySQL

### Étape 4 : Configuration de l'application

#### 4.1 Vérification de la configuration
Éditer le fichier `src/utils/DatabaseConnection.java` si nécessaire :
```java
private static final String URL = "*********************************";
private static final String USER = "root";
private static final String PASSWORD = "";  // Votre mot de passe MySQL
```

#### 4.2 Installation des dépendances
```bash
cd gym-management-system
mvn clean install
```

### Étape 5 : Compilation et exécution

#### Méthode 1 - Scripts fournis (Recommandé)
```bash
# Windows
run-app.bat

# Linux/macOS
chmod +x run-app.sh
./run-app.sh
```

#### Méthode 2 - Maven
```bash
mvn clean compile
mvn exec:java -Dexec.mainClass="com.gymmanagement.Main"
```

#### Méthode 3 - IDE
1. Importer le projet dans votre IDE (IntelliJ IDEA, Eclipse, VS Code)
2. Configurer le JDK 11+
3. Exécuter la classe `Main.java`

## 🚀 Déploiement en production

### Configuration de production

#### 1. Sécurisation de la base de données
```sql
-- Créer un utilisateur dédié
CREATE USER 'gymapp'@'localhost' IDENTIFIED BY 'mot_de_passe_fort';
GRANT ALL PRIVILEGES ON gymdb.* TO 'gymapp'@'localhost';
FLUSH PRIVILEGES;
```

#### 2. Configuration de l'application
Créer un fichier `config.properties` :
```properties
db.url=*********************************
db.username=gymapp
db.password=mot_de_passe_fort
db.pool.size=10
```

#### 3. Création d'un exécutable
```bash
# Avec Maven
mvn clean package

# Création d'un JAR exécutable
mvn clean compile assembly:single
```

### Script de déploiement automatique

Créer `deploy.bat` (Windows) ou `deploy.sh` (Linux/macOS) :
```bash
#!/bin/bash
echo "Déploiement du Système de Gestion de Salle de Sport"

# Vérification des prérequis
java -version || { echo "Java non installé"; exit 1; }

# Compilation
mvn clean package || { echo "Erreur de compilation"; exit 1; }

# Démarrage de MySQL
systemctl start mysql  # Linux
# ou
net start mysql        # Windows

# Exécution de l'application
java -jar target/gym-management-system-1.0-SNAPSHOT.jar

echo "Déploiement terminé avec succès"
```

## 🔍 Vérification de l'installation

### Tests de fonctionnement

#### 1. Test de connexion à la base de données
```sql
-- Vérifier les tables créées
SHOW TABLES;

-- Vérifier les données de test
SELECT * FROM users;
SELECT * FROM membres;
SELECT * FROM abonnements;
SELECT * FROM inscriptions;
```

#### 2. Test de l'application
1. **Démarrage** : L'application doit s'ouvrir sans erreur
2. **Connexion** : Utiliser `admin` / `admin`
3. **Navigation** : Tester l'accès à tous les modules
4. **Données** : Vérifier l'affichage des données de test

### Résolution des problèmes courants

#### Erreur de connexion à la base de données
```
Solution 1: Vérifier que MySQL est démarré
Solution 2: Vérifier les paramètres de connexion
Solution 3: Vérifier les permissions utilisateur
```

#### Erreur JavaFX
```
Solution 1: Vérifier la version de Java (11+ requis)
Solution 2: Ajouter les modules JavaFX au classpath
Solution 3: Utiliser --module-path et --add-modules
```

#### Erreur de compilation Maven
```
Solution 1: mvn clean install -U
Solution 2: Vérifier la configuration du JDK
Solution 3: Supprimer le dossier .m2/repository et relancer
```

## 📚 Configuration avancée

### Optimisation des performances

#### Configuration MySQL
```sql
-- Dans my.cnf ou my.ini
[mysqld]
innodb_buffer_pool_size = 256M
max_connections = 100
query_cache_size = 64M
```

#### Configuration JVM
```bash
java -Xms512m -Xmx1024m -jar gym-management-system.jar
```

### Sauvegarde automatique
```bash
#!/bin/bash
# Script de sauvegarde quotidienne
mysqldump -u root -p gymdb > backup_$(date +%Y%m%d).sql
```

### Monitoring
```bash
# Surveillance des logs
tail -f application.log

# Surveillance des performances
top -p $(pgrep java)
```

## 🔒 Sécurité

### Recommandations de sécurité
1. **Changer les mots de passe par défaut**
2. **Utiliser HTTPS en production**
3. **Configurer un firewall**
4. **Mettre à jour régulièrement**
5. **Sauvegarder régulièrement**

### Configuration SSL (optionnel)
```properties
# Pour une future version web
server.ssl.enabled=true
server.ssl.key-store=keystore.p12
server.ssl.key-store-password=password
```

## 📞 Support et maintenance

### Contacts support
- **Documentation** : Consulter le README.md
- **Issues** : Créer un ticket sur le repository
- **Email** : <EMAIL>

### Maintenance recommandée
- **Quotidienne** : Vérification des logs
- **Hebdomadaire** : Sauvegarde de la base de données
- **Mensuelle** : Mise à jour des dépendances
- **Trimestrielle** : Audit de sécurité

---

**Version du guide** : 1.0  
**Dernière mise à jour** : $(date)  
**Compatibilité** : Gym Management System v1.0-SNAPSHOT
