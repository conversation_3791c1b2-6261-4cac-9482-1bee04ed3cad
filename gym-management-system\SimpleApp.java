import javafx.application.Application;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class SimpleApp extends Application {

    private static final String DB_URL = "*********************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "";

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("Gym Management System - Simple Test");

        Label statusLabel = new Label("Database Status: Not Connected");
        Button connectButton = new Button("Connect to Database");
        
        connectButton.setOnAction(e -> {
            try {
                Connection connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                statusLabel.setText("Database Status: Connected Successfully!");
                
                // Try to query the database
                Statement statement = connection.createStatement();
                ResultSet resultSet = statement.executeQuery("SHOW TABLES");
                
                StringBuilder tables = new StringBuilder("Tables in database:\n");
                while (resultSet.next()) {
                    tables.append("- ").append(resultSet.getString(1)).append("\n");
                }
                
                statusLabel.setText(tables.toString());
                
                connection.close();
            } catch (Exception ex) {
                statusLabel.setText("Database Status: Connection Failed!\n" + ex.getMessage());
                ex.printStackTrace();
            }
        });

        VBox root = new VBox(10);
        root.getChildren().addAll(statusLabel, connectButton);
        
        Scene scene = new Scene(root, 400, 300);
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
