import java.util.List;

public class Subscription {
    private int id;
    private String type;
    private int dureeJours;
    private double prix;
    private List<Registration> registrations; // One-to-many relationship

    public Subscription(int id, String type, int dureeJours, double prix) {
        this.id = id;
        this.type = type;
        this.dureeJours = dureeJours;
        this.prix = prix;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getDureeJours() {
        return dureeJours;
    }

    public void setDureeJours(int dureeJours) {
        this.dureeJours = dureeJours;
    }

    public double getPrix() {
        return prix;
    }

    public void setPrix(double prix) {
        this.prix = prix;
    }

    public List<Registration> getRegistrations() {
        return registrations;
    }

    public void setRegistrations(List<Registration> registrations) {
        this.registrations = registrations;
    }
}