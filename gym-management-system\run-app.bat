@echo off
echo Setting up the project...

:: Create the lib directory if it doesn't exist
if not exist "lib" mkdir lib

:: Download JavaFX SDK if it doesn't exist
if not exist "lib\javafx-sdk-17" (
    echo Downloading JavaFX SDK...
    powershell -Command "Invoke-WebRequest -Uri 'https://download2.gluonhq.com/openjfx/17/openjfx-17_windows-x64_bin-sdk.zip' -OutFile 'lib\javafx-sdk-17.zip'"
    echo Extracting JavaFX SDK...
    powershell -Command "Expand-Archive -Path 'lib\javafx-sdk-17.zip' -DestinationPath 'lib'"
    echo JavaFX SDK downloaded and extracted.
)

:: Download MySQL Connector if it doesn't exist
if not exist "lib\mysql-connector-java-8.0.27.jar" (
    echo Downloading MySQL Connector...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.27/mysql-connector-java-8.0.27.jar' -OutFile 'lib\mysql-connector-java-8.0.27.jar'"
    echo MySQL Connector downloaded.
)

echo Compiling the application...

:: Compile the application
javac --module-path lib\javafx-sdk-17\lib --add-modules javafx.controls,javafx.fxml -cp ".;lib\mysql-connector-java-8.0.27.jar" *.java

:: Check if compilation was successful
if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b %errorlevel%
)

echo Compilation successful!
echo Running the application...

:: Run the application
java --module-path lib\javafx-sdk-17\lib --add-modules javafx.controls,javafx.fxml -cp ".;lib\mysql-connector-java-8.0.27.jar" LoginScreen

pause
