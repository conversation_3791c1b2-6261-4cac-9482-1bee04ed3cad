import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;

import java.sql.*;

public class RegistrationScreen {

    private Stage primaryStage;
    private LoginScreen loginScreen;
    private TextField nameField;
    private TextField emailField;
    private PasswordField passwordField;
    private PasswordField confirmPasswordField;
    private Label statusLabel;

    public RegistrationScreen(Stage primaryStage, LoginScreen loginScreen) {
        this.primaryStage = primaryStage;
        this.loginScreen = loginScreen;
        primaryStage.setTitle("Salle de Sport - Inscription");
    }

    public void show() {
        // Create the registration form
        VBox registrationForm = createRegistrationForm();

        // Create the scene
        Scene scene = new Scene(registrationForm, 400, 350);
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    private VBox createRegistrationForm() {
        // Create form elements
        Label titleLabel = new Label("Inscription");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");

        Label nameLabel = new Label("Nom:");
        nameField = new TextField();
        nameField.setPromptText("Entrez votre nom");

        Label emailLabel = new Label("Email:");
        emailField = new TextField();
        emailField.setPromptText("Entrez votre email");

        Label passwordLabel = new Label("Mot de passe:");
        passwordField = new PasswordField();
        passwordField.setPromptText("Choisissez un mot de passe");

        Label confirmPasswordLabel = new Label("Confirmer le mot de passe:");
        confirmPasswordField = new PasswordField();
        confirmPasswordField.setPromptText("Confirmez votre mot de passe");

        Button registerButton = new Button("S'inscrire");
        registerButton.setOnAction(e -> handleRegistration());

        Button backButton = new Button("Retour");
        backButton.setOnAction(e -> returnToLogin());

        statusLabel = new Label("");
        statusLabel.setStyle("-fx-text-fill: red;");

        // Create a horizontal box for buttons
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().addAll(registerButton, backButton);

        // Create the form layout
        VBox registrationForm = new VBox(10);
        registrationForm.setPadding(new Insets(20));
        registrationForm.setAlignment(Pos.CENTER);
        registrationForm.getChildren().addAll(
            titleLabel,
            nameLabel, nameField,
            emailLabel, emailField,
            passwordLabel, passwordField,
            confirmPasswordLabel, confirmPasswordField,
            buttonBox,
            statusLabel
        );

        return registrationForm;
    }

    private void handleRegistration() {
        String name = nameField.getText();
        String email = emailField.getText();
        String password = passwordField.getText();
        String confirmPassword = confirmPasswordField.getText();

        // Validate input
        if (name.isEmpty() || email.isEmpty() || password.isEmpty() || confirmPassword.isEmpty()) {
            statusLabel.setText("Veuillez remplir tous les champs");
            return;
        }

        if (!password.equals(confirmPassword)) {
            statusLabel.setText("Les mots de passe ne correspondent pas");
            return;
        }

        // Check if email already exists
        if (emailExists(email)) {
            statusLabel.setText("Cet email existe déjà");
            return;
        }

        // Register the user
        if (registerUser(name, email, password)) {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Inscription réussie");
            alert.setHeaderText(null);
            alert.setContentText("Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter.");
            alert.showAndWait();

            returnToLogin();
        } else {
            statusLabel.setText("Erreur lors de l'inscription");
        }
    }

    private boolean emailExists(String email) {
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "SELECT * FROM users WHERE email = ?";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, email);

            ResultSet resultSet = statement.executeQuery();
            return resultSet.next(); // Returns true if email exists
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur de connexion à la base de données");
            return false;
        }
    }

    private boolean registerUser(String name, String email, String password) {
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "INSERT INTO users (name, email, password) VALUES (?, ?, ?)";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, name);
            statement.setString(2, email);
            statement.setString(3, password);

            int rowsInserted = statement.executeUpdate();
            return rowsInserted > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur de connexion à la base de données");
            return false;
        }
    }

    private void returnToLogin() {
        loginScreen.show();
    }
}
