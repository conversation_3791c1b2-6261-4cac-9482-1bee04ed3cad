package com.gymmanagement.controllers;

import com.gymmanagement.models.Registration;
import com.gymmanagement.utils.DatabaseConnection;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class RegistrationController {

    private DatabaseConnection databaseConnection;

    public RegistrationController() {
        databaseConnection = new DatabaseConnection();
    }

    public void registerMember(Registration registration) {
        String query = "INSERT INTO inscriptions (membre_id, abonnement_id, date_debut, date_fin, etat) VALUES (?, ?, ?, ?, ?)";
        try (Connection connection = databaseConnection.connect();
             PreparedStatement statement = connection.prepareStatement(query)) {
            statement.setInt(1, registration.getMembreId());
            statement.setInt(2, registration.getAbonnementId());
            statement.setDate(3, java.sql.Date.valueOf(registration.getDateDebut()));
            statement.setDate(4, java.sql.Date.valueOf(registration.getDateFin()));
            statement.setString(5, registration.getEtat());
            statement.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public List<Registration> getRegistrations() {
        List<Registration> registrations = new ArrayList<>();
        String query = "SELECT * FROM inscriptions";
        try (Connection connection = databaseConnection.connect();
             PreparedStatement statement = connection.prepareStatement(query);
             ResultSet resultSet = statement.executeQuery()) {
            while (resultSet.next()) {
                Registration registration = new Registration();
                registration.setId(resultSet.getInt("id"));
                registration.setMembreId(resultSet.getInt("membre_id"));
                registration.setAbonnementId(resultSet.getInt("abonnement_id"));
                registration.setDateDebut(resultSet.getDate("date_debut").toLocalDate());
                registration.setDateFin(resultSet.getDate("date_fin").toLocalDate());
                registration.setEtat(resultSet.getString("etat"));
                registrations.add(registration);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return registrations;
    }
}