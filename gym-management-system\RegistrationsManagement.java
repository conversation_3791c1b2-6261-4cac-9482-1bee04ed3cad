import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.*;
import javafx.stage.Stage;

import java.sql.*;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

public class RegistrationsManagement {

    private Stage primaryStage;
    private MainDashboard dashboard;
    private TableView<Registration> registrationTable;
    private TextField idField;
    private ComboBox<String> membreComboBox, abonnementComboBox, etatComboBox;
    private DatePicker dateDebutPicker, dateFinPicker;
    private Label statusLabel;
    private Map<String, Integer> membreMap = new HashMap<>();
    private Map<String, Integer> abonnementMap = new HashMap<>();

    public RegistrationsManagement(Stage primaryStage, MainDashboard dashboard) {
        this.primaryStage = primaryStage;
        this.dashboard = dashboard;
        primaryStage.setTitle("Gestion de la Salle de Sport - Gestion des Inscriptions");
    }

    public void show() {
        // Create the registrations management layout
        BorderPane layout = createLayout();

        // Create the scene
        Scene scene = new Scene(layout, 800, 600);
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    private BorderPane createLayout() {
        // Create title
        Label titleLabel = new Label("Gestion des Inscriptions");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");

        // Create form for adding/editing registrations
        GridPane form = createForm();

        // Create table for displaying registrations
        registrationTable = createTable();

        // Create buttons
        HBox buttonBox = createButtonBox();

        // Create status label
        statusLabel = new Label("");
        statusLabel.setStyle("-fx-text-fill: red;");

        // Create back button
        Button backButton = new Button("Retour au tableau de bord");
        backButton.setOnAction(e -> returnToDashboard());

        // Create layout
        VBox topSection = new VBox(10);
        topSection.setAlignment(Pos.CENTER);
        topSection.getChildren().addAll(titleLabel, form, buttonBox, statusLabel);

        VBox bottomSection = new VBox(10);
        bottomSection.setAlignment(Pos.CENTER);
        bottomSection.getChildren().addAll(backButton);

        BorderPane layout = new BorderPane();
        layout.setPadding(new Insets(20));
        layout.setTop(topSection);
        layout.setCenter(registrationTable);
        layout.setBottom(bottomSection);

        // Load members and subscriptions for combo boxes
        loadMembres();
        loadAbonnements();

        // Load registrations from database
        loadRegistrations();

        return layout;
    }

    private GridPane createForm() {
        // Create form elements
        Label idLabel = new Label("ID:");
        idField = new TextField();
        idField.setEditable(false);
        idField.setPromptText("Auto-généré");

        Label membreLabel = new Label("Membre:");
        membreComboBox = new ComboBox<>();
        membreComboBox.setPromptText("Sélectionnez un membre");

        Label abonnementLabel = new Label("Abonnement:");
        abonnementComboBox = new ComboBox<>();
        abonnementComboBox.setPromptText("Sélectionnez un abonnement");
        abonnementComboBox.setOnAction(e -> updateDateFin());

        Label dateDebutLabel = new Label("Date de début:");
        dateDebutPicker = new DatePicker(LocalDate.now());
        dateDebutPicker.setOnAction(e -> updateDateFin());

        Label dateFinLabel = new Label("Date de fin:");
        dateFinPicker = new DatePicker();

        Label etatLabel = new Label("État:");
        etatComboBox = new ComboBox<>();
        etatComboBox.getItems().addAll("Actif", "Inactif", "En attente", "Expiré");
        etatComboBox.setValue("Actif");

        // Create form layout
        GridPane form = new GridPane();
        form.setHgap(10);
        form.setVgap(10);
        form.setPadding(new Insets(10));

        // Add form elements to layout
        form.add(idLabel, 0, 0);
        form.add(idField, 1, 0);
        form.add(membreLabel, 0, 1);
        form.add(membreComboBox, 1, 1);
        form.add(abonnementLabel, 2, 1);
        form.add(abonnementComboBox, 3, 1);
        form.add(dateDebutLabel, 0, 2);
        form.add(dateDebutPicker, 1, 2);
        form.add(dateFinLabel, 2, 2);
        form.add(dateFinPicker, 3, 2);
        form.add(etatLabel, 0, 3);
        form.add(etatComboBox, 1, 3);

        return form;
    }

    private TableView<Registration> createTable() {
        // Create table columns
        TableColumn<Registration, Integer> idColumn = new TableColumn<>("ID");
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));

        TableColumn<Registration, String> membreColumn = new TableColumn<>("Membre");
        membreColumn.setCellValueFactory(new PropertyValueFactory<>("membreNom"));

        TableColumn<Registration, String> abonnementColumn = new TableColumn<>("Abonnement");
        abonnementColumn.setCellValueFactory(new PropertyValueFactory<>("abonnementType"));

        TableColumn<Registration, LocalDate> dateDebutColumn = new TableColumn<>("Date de début");
        dateDebutColumn.setCellValueFactory(new PropertyValueFactory<>("dateDebut"));

        TableColumn<Registration, LocalDate> dateFinColumn = new TableColumn<>("Date de fin");
        dateFinColumn.setCellValueFactory(new PropertyValueFactory<>("dateFin"));

        TableColumn<Registration, String> etatColumn = new TableColumn<>("État");
        etatColumn.setCellValueFactory(new PropertyValueFactory<>("etat"));

        // Create table
        TableView<Registration> table = new TableView<>();
        table.getColumns().addAll(idColumn, membreColumn, abonnementColumn, dateDebutColumn, dateFinColumn, etatColumn);

        // Set column widths
        idColumn.setPrefWidth(50);
        membreColumn.setPrefWidth(150);
        abonnementColumn.setPrefWidth(150);
        dateDebutColumn.setPrefWidth(120);
        dateFinColumn.setPrefWidth(120);
        etatColumn.setPrefWidth(100);

        // Add selection listener
        table.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            if (newSelection != null) {
                // Fill form with selected registration data
                idField.setText(String.valueOf(newSelection.getId()));

                // Find and select the membre in the combo box
                for (String membreNom : membreMap.keySet()) {
                    if (membreMap.get(membreNom) == newSelection.getMembreId()) {
                        membreComboBox.setValue(membreNom);
                        break;
                    }
                }

                // Find and select the abonnement in the combo box
                for (String abonnementType : abonnementMap.keySet()) {
                    if (abonnementMap.get(abonnementType) == newSelection.getAbonnementId()) {
                        abonnementComboBox.setValue(abonnementType);
                        break;
                    }
                }

                dateDebutPicker.setValue(newSelection.getDateDebut());
                dateFinPicker.setValue(newSelection.getDateFin());
                etatComboBox.setValue(newSelection.getEtat());
            }
        });

        return table;
    }

    private HBox createButtonBox() {
        // Create buttons
        Button addButton = new Button("Ajouter");
        addButton.setOnAction(e -> addRegistration());

        Button updateButton = new Button("Modifier");
        updateButton.setOnAction(e -> updateRegistration());

        Button deleteButton = new Button("Supprimer");
        deleteButton.setOnAction(e -> deleteRegistration());

        Button clearButton = new Button("Effacer");
        clearButton.setOnAction(e -> clearForm());

        // Create button layout
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().addAll(addButton, updateButton, deleteButton, clearButton);

        return buttonBox;
    }

    private void loadMembres() {
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "SELECT id, nom, prenom FROM membres";
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(query);

            membreMap.clear();
            membreComboBox.getItems().clear();

            while (resultSet.next()) {
                int id = resultSet.getInt("id");
                String nom = resultSet.getString("nom");
                String prenom = resultSet.getString("prenom");
                String fullName = nom + " " + prenom;

                membreMap.put(fullName, id);
                membreComboBox.getItems().add(fullName);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors du chargement des membres: " + e.getMessage());
        }
    }

    private void loadAbonnements() {
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "SELECT id, type, duree_jours FROM abonnements";
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(query);

            abonnementMap.clear();
            abonnementComboBox.getItems().clear();

            while (resultSet.next()) {
                int id = resultSet.getInt("id");
                String type = resultSet.getString("type");

                abonnementMap.put(type, id);
                abonnementComboBox.getItems().add(type);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors du chargement des abonnements: " + e.getMessage());
        }
    }

    private void loadRegistrations() {
        ObservableList<Registration> registrations = FXCollections.observableArrayList();

        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            // First check if the inscriptions table exists
            try {
                Statement checkStatement = connection.createStatement();

                // Check if etat column exists, if not, add it
                try {
                    ResultSet columnsResult = connection.getMetaData().getColumns(null, null, "inscriptions", "etat");
                    if (!columnsResult.next()) {
                        System.out.println("Column 'etat' not found, adding it to the table");
                        checkStatement.executeUpdate("ALTER TABLE inscriptions ADD COLUMN etat VARCHAR(20) DEFAULT 'Actif' NOT NULL");
                    }
                } catch (SQLException e) {
                    e.printStackTrace();
                    statusLabel.setText("Erreur lors de la vérification de la colonne etat: " + e.getMessage());
                }

                // Check if there are any registrations
                ResultSet checkResult = checkStatement.executeQuery("SELECT COUNT(*) FROM inscriptions");
                checkResult.next();
                int count = checkResult.getInt(1);
                System.out.println("Number of inscriptions found: " + count);

                if (count == 0) {
                    statusLabel.setText("Aucune inscription trouvée dans la base de données");
                    return;
                }

                // Update expired registrations
                LocalDate today = LocalDate.now();
                String updateQuery = "UPDATE inscriptions SET etat = 'Expiré' WHERE date_fin < ? AND etat = 'Actif'";
                PreparedStatement updateStatement = connection.prepareStatement(updateQuery);
                updateStatement.setDate(1, java.sql.Date.valueOf(today));
                int updatedRows = updateStatement.executeUpdate();
                if (updatedRows > 0) {
                    System.out.println(updatedRows + " inscriptions expirées mises à jour");
                }

            } catch (SQLException e) {
                e.printStackTrace();
                statusLabel.setText("Erreur lors de la vérification des inscriptions: " + e.getMessage());
                return;
            }

            // If we get here, the table exists and has data
            String query = "SELECT i.*, m.nom, m.prenom, a.type " +
                           "FROM inscriptions i " +
                           "JOIN membres m ON i.membre_id = m.id " +
                           "JOIN abonnements a ON i.abonnement_id = a.id";

            try {
                Statement statement = connection.createStatement();
                ResultSet resultSet = statement.executeQuery(query);

                while (resultSet.next()) {
                    LocalDate dateFin = resultSet.getDate("date_fin").toLocalDate();
                    String etat = resultSet.getString("etat");

                    // Double-check if the registration is expired
                    if (dateFin.isBefore(LocalDate.now()) && "Actif".equals(etat)) {
                        etat = "Expiré";

                        // Update the database
                        PreparedStatement updateStatement = connection.prepareStatement(
                            "UPDATE inscriptions SET etat = 'Expiré' WHERE id = ?");
                        updateStatement.setInt(1, resultSet.getInt("id"));
                        updateStatement.executeUpdate();
                    }

                    Registration registration = new Registration(
                        resultSet.getInt("id"),
                        resultSet.getInt("membre_id"),
                        resultSet.getString("nom") + " " + resultSet.getString("prenom"),
                        resultSet.getInt("abonnement_id"),
                        resultSet.getString("type"),
                        resultSet.getDate("date_debut").toLocalDate(),
                        dateFin,
                        etat
                    );
                    registrations.add(registration);
                    System.out.println("Added registration: " + registration.getId() + " - " + registration.getMembreNom() + " - " + etat);
                }

                registrationTable.setItems(registrations);
                System.out.println("Total registrations loaded: " + registrations.size());
            } catch (SQLException e) {
                e.printStackTrace();
                statusLabel.setText("Erreur lors de la requête des inscriptions: " + e.getMessage());
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur de connexion à la base de données: " + e.getMessage());
        }
    }

    private void updateDateFin() {
        if (dateDebutPicker.getValue() == null || abonnementComboBox.getValue() == null) {
            return;
        }

        LocalDate dateDebut = dateDebutPicker.getValue();
        String abonnementType = abonnementComboBox.getValue();

        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "SELECT duree_jours FROM abonnements WHERE type = ?";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, abonnementType);
            ResultSet resultSet = statement.executeQuery();

            if (resultSet.next()) {
                int dureeJours = resultSet.getInt("duree_jours");
                LocalDate dateFin = dateDebut.plusDays(dureeJours);
                dateFinPicker.setValue(dateFin);
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors du calcul de la date de fin: " + e.getMessage());
        }
    }

    private void addRegistration() {
        // Validate input
        if (!validateInput()) {
            return;
        }

        String membreNom = membreComboBox.getValue();
        String abonnementType = abonnementComboBox.getValue();
        LocalDate dateDebut = dateDebutPicker.getValue();
        LocalDate dateFin = dateFinPicker.getValue();
        String etat = etatComboBox.getValue();

        // Check if the registration is already expired
        if (dateFin.isBefore(LocalDate.now())) {
            etat = "Expiré";
            etatComboBox.setValue("Expiré");
            statusLabel.setText("Attention: La date de fin est déjà passée, l'état a été défini sur 'Expiré'");
        }

        int membreId = membreMap.get(membreNom);
        int abonnementId = abonnementMap.get(abonnementType);

        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "INSERT INTO inscriptions (membre_id, abonnement_id, date_debut, date_fin, etat) VALUES (?, ?, ?, ?, ?)";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setInt(1, membreId);
            statement.setInt(2, abonnementId);
            statement.setDate(3, java.sql.Date.valueOf(dateDebut));
            statement.setDate(4, java.sql.Date.valueOf(dateFin));
            statement.setString(5, etat);

            int rowsInserted = statement.executeUpdate();
            if (rowsInserted > 0) {
                statusLabel.setText("Inscription ajoutée avec succès");
                clearForm();
                loadRegistrations();
            } else {
                statusLabel.setText("Erreur lors de l'ajout de l'inscription");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors de l'ajout de l'inscription: " + e.getMessage());
        }
    }

    private void updateRegistration() {
        // Check if a registration is selected
        if (idField.getText().isEmpty()) {
            statusLabel.setText("Veuillez sélectionner une inscription à modifier");
            return;
        }

        // Validate input
        if (!validateInput()) {
            return;
        }

        int id = Integer.parseInt(idField.getText());
        String membreNom = membreComboBox.getValue();
        String abonnementType = abonnementComboBox.getValue();
        LocalDate dateDebut = dateDebutPicker.getValue();
        LocalDate dateFin = dateFinPicker.getValue();
        String etat = etatComboBox.getValue();

        // Check if the registration is already expired
        if (dateFin.isBefore(LocalDate.now()) && !"Expiré".equals(etat)) {
            etat = "Expiré";
            etatComboBox.setValue("Expiré");
            statusLabel.setText("Attention: La date de fin est déjà passée, l'état a été défini sur 'Expiré'");
        }

        int membreId = membreMap.get(membreNom);
        int abonnementId = abonnementMap.get(abonnementType);

        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "UPDATE inscriptions SET membre_id = ?, abonnement_id = ?, date_debut = ?, date_fin = ?, etat = ? WHERE id = ?";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setInt(1, membreId);
            statement.setInt(2, abonnementId);
            statement.setDate(3, java.sql.Date.valueOf(dateDebut));
            statement.setDate(4, java.sql.Date.valueOf(dateFin));
            statement.setString(5, etat);
            statement.setInt(6, id);

            int rowsUpdated = statement.executeUpdate();
            if (rowsUpdated > 0) {
                statusLabel.setText("Inscription modifiée avec succès");
                clearForm();
                loadRegistrations();
            } else {
                statusLabel.setText("Erreur lors de la modification de l'inscription");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors de la modification de l'inscription: " + e.getMessage());
        }
    }

    private void deleteRegistration() {
        // Check if a registration is selected
        if (idField.getText().isEmpty()) {
            statusLabel.setText("Veuillez sélectionner une inscription à supprimer");
            return;
        }

        int id = Integer.parseInt(idField.getText());

        // Confirm deletion
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Confirmation de suppression");
        alert.setHeaderText(null);
        alert.setContentText("Êtes-vous sûr de vouloir supprimer cette inscription ?");

        if (alert.showAndWait().get() == ButtonType.OK) {
            String dbUrl = "*********************************";
            String dbUser = "root";
            String dbPassword = "";

            try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
                String query = "DELETE FROM inscriptions WHERE id = ?";
                PreparedStatement statement = connection.prepareStatement(query);
                statement.setInt(1, id);

                int rowsDeleted = statement.executeUpdate();
                if (rowsDeleted > 0) {
                    statusLabel.setText("Inscription supprimée avec succès");
                    clearForm();
                    loadRegistrations();
                } else {
                    statusLabel.setText("Erreur lors de la suppression de l'inscription");
                }
            } catch (SQLException e) {
                e.printStackTrace();
                statusLabel.setText("Erreur lors de la suppression de l'inscription: " + e.getMessage());
            }
        }
    }

    private boolean validateInput() {
        if (membreComboBox.getValue() == null) {
            statusLabel.setText("Veuillez sélectionner un membre");
            return false;
        }

        if (abonnementComboBox.getValue() == null) {
            statusLabel.setText("Veuillez sélectionner un abonnement");
            return false;
        }

        if (dateDebutPicker.getValue() == null) {
            statusLabel.setText("Veuillez sélectionner une date de début");
            return false;
        }

        if (dateFinPicker.getValue() == null) {
            statusLabel.setText("Veuillez sélectionner une date de fin");
            return false;
        }

        if (etatComboBox.getValue() == null) {
            statusLabel.setText("Veuillez sélectionner un état");
            return false;
        }

        return true;
    }

    private void clearForm() {
        idField.clear();
        membreComboBox.setValue(null);
        abonnementComboBox.setValue(null);
        dateDebutPicker.setValue(LocalDate.now());
        dateFinPicker.setValue(null);
        etatComboBox.setValue("Actif");
        registrationTable.getSelectionModel().clearSelection();
    }

    private void returnToDashboard() {
        dashboard.show();
    }
}
