import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.stage.Stage;

public class MainDashboard {

    private Stage primaryStage;

    public MainDashboard(Stage primaryStage) {
        this.primaryStage = primaryStage;
        primaryStage.setTitle("Gestion de la Salle de Sport");
    }

    public void show() {
        // Create the dashboard layout
        VBox dashboard = createDashboard();
        
        // Create the scene
        Scene scene = new Scene(dashboard, 800, 600);
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    private VBox createDashboard() {
        // Create title
        Label titleLabel = new Label("Gestion de la Salle de Sport");
        titleLabel.setStyle("-fx-font-size: 32px; -fx-font-weight: bold;");
        
        // Create menu buttons
        Button membersButton = createMenuButton("Gestion des Membres", "#3498db");
        membersButton.setOnAction(e -> showMembersManagement());
        
        Button subscriptionsButton = createMenuButton("Gestion des Abonnements", "#2ecc71");
        subscriptionsButton.setOnAction(e -> showSubscriptionsManagement());
        
        Button registrationsButton = createMenuButton("Gestion des Inscriptions", "#9b59b6");
        registrationsButton.setOnAction(e -> showRegistrationsManagement());
        
        Button logoutButton = createMenuButton("Déconnexion", "#e74c3c");
        logoutButton.setOnAction(e -> logout());
        
        // Create the dashboard layout
        VBox dashboard = new VBox(20);
        dashboard.setPadding(new Insets(30));
        dashboard.setAlignment(Pos.CENTER);
        dashboard.getChildren().addAll(
            titleLabel,
            membersButton,
            subscriptionsButton,
            registrationsButton,
            logoutButton
        );
        
        return dashboard;
    }
    
    private Button createMenuButton(String text, String colorHex) {
        Button button = new Button(text);
        button.setPrefWidth(600);
        button.setPrefHeight(80);
        button.setStyle(
            "-fx-background-color: " + colorHex + ";" +
            "-fx-text-fill: white;" +
            "-fx-font-size: 20px;" +
            "-fx-font-weight: bold;"
        );
        return button;
    }
    
    private void showMembersManagement() {
        MembersManagement membersManagement = new MembersManagement(primaryStage, this);
        membersManagement.show();
    }
    
    private void showSubscriptionsManagement() {
        SubscriptionsManagement subscriptionsManagement = new SubscriptionsManagement(primaryStage, this);
        subscriptionsManagement.show();
    }
    
    private void showRegistrationsManagement() {
        RegistrationsManagement registrationsManagement = new RegistrationsManagement(primaryStage, this);
        registrationsManagement.show();
    }
    
    private void logout() {
        // Return to login screen
        LoginScreen loginScreen = new LoginScreen();
        loginScreen.start(primaryStage);
    }
}
