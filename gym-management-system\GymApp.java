import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.stage.Stage;

import java.io.File;
import java.net.URL;

public class GymApp extends Application {
    @Override
    public void start(Stage primaryStage) throws Exception {
        // Load the FXML file
        URL url = new File("src/views/MemberView.fxml").toURI().toURL();
        FXMLLoader loader = new FXMLLoader(url);
        Scene scene = new Scene(loader.load());
        
        primaryStage.setScene(scene);
        primaryStage.setTitle("Gym Management System");
        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
