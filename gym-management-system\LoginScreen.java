import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;

import java.sql.*;

public class LoginScreen extends Application {

    private Stage primaryStage;
    private TextField emailField;
    private PasswordField passwordField;
    private Label statusLabel;

    @Override
    public void start(Stage primaryStage) {
        this.primaryStage = primaryStage;
        primaryStage.setTitle("Salle de Sport - Connexion");

        // Create the login form
        VBox loginForm = createLoginForm();

        // Create the scene
        Scene scene = new Scene(loginForm, 400, 300);
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    private VBox createLoginForm() {
        // Create form elements
        Label titleLabel = new Label("Connexion");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");

        Label emailLabel = new Label("Email:");
        emailField = new TextField();
        emailField.setPromptText("Entrez votre email");

        Label passwordLabel = new Label("Mot de passe:");
        passwordField = new PasswordField();
        passwordField.setPromptText("Entrez votre mot de passe");

        Button loginButton = new Button("Se connecter");
        loginButton.setOnAction(e -> handleLogin());

        Button registerButton = new Button("S'inscrire");
        registerButton.setOnAction(e -> showRegistrationScreen());

        statusLabel = new Label("");
        statusLabel.setStyle("-fx-text-fill: red;");

        // Create a horizontal box for buttons
        HBox buttonBox = new HBox(10);
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().addAll(loginButton, registerButton);

        // Create the form layout
        VBox loginForm = new VBox(10);
        loginForm.setPadding(new Insets(20));
        loginForm.setAlignment(Pos.CENTER);
        loginForm.getChildren().addAll(
            titleLabel,
            emailLabel, emailField,
            passwordLabel, passwordField,
            buttonBox,
            statusLabel
        );

        return loginForm;
    }

    private void handleLogin() {
        String email = emailField.getText();
        String password = passwordField.getText();

        if (email.isEmpty() || password.isEmpty()) {
            statusLabel.setText("Veuillez remplir tous les champs");
            return;
        }

        // Check credentials in the database
        if (validateCredentials(email, password)) {
            // Show the main dashboard
            showMainDashboard();
        } else {
            statusLabel.setText("Email ou mot de passe incorrect");
        }
    }

    private boolean validateCredentials(String email, String password) {
        String dbUrl = "*********************************";
        String dbUser = "root";
        String dbPassword = "";

        try (Connection connection = DriverManager.getConnection(dbUrl, dbUser, dbPassword)) {
            String query = "SELECT * FROM users WHERE email = ? AND password = ?";
            PreparedStatement statement = connection.prepareStatement(query);
            statement.setString(1, email);
            statement.setString(2, password);

            ResultSet resultSet = statement.executeQuery();
            return resultSet.next(); // Returns true if a matching user was found
        } catch (SQLException e) {
            e.printStackTrace();
            statusLabel.setText("Erreur de connexion à la base de données");
            return false;
        }
    }

    private void showRegistrationScreen() {
        // Create and show the registration screen
        RegistrationScreen registrationScreen = new RegistrationScreen(primaryStage, this);
        registrationScreen.show();
    }

    private void showMainDashboard() {
        // Create and show the main dashboard
        MainDashboard dashboard = new MainDashboard(primaryStage);
        dashboard.show();
    }

    public void show() {
        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}
