<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.fxml.FXMLLoader?>

<AnchorPane xmlns:fx="http://javafx.com/fxml" prefHeight="400.0" prefWidth="600.0">
    <GridPane alignment="CENTER" hgap="10" vgap="10">
        <Label text="Nom:" />
        <TextField fx:id="nomField" GridPane.columnIndex="1" />
        
        <Label text="Prénom:" GridPane.rowIndex="1" />
        <TextField fx:id="prenomField" GridPane.rowIndex="1" GridPane.columnIndex="1" />
        
        <Label text="Email:" GridPane.rowIndex="2" />
        <TextField fx:id="emailField" GridPane.rowIndex="2" GridPane.columnIndex="1" />
        
        <Label text="Téléphone:" GridPane.rowIndex="3" />
        <TextField fx:id="telephoneField" GridPane.rowIndex="3" GridPane.columnIndex="1" />
        
        <Button text="Ajouter Membre" onAction="#handleAddMember" GridPane.rowIndex="4" GridPane.columnSpan="2" />
        
        <TableView fx:id="membersTable" GridPane.rowIndex="5" GridPane.columnSpan="2">
            <!-- Define columns for the TableView here -->
        </TableView>
    </GridPane>
</AnchorPane>

FXMLLoader loader = new FXMLLoader(getClass().getResource("/views/MemberView.fxml"));